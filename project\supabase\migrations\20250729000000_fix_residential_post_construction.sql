/*
  Fix Missing Residential Post-Construction Service Type
  
  CRITICAL FIX: The latest migration accidentally removed 'residential_post_construction' 
  from the allowed service types, causing booking failures.
  
  This migration adds it back to fix the constraint violation error.
*/

-- Add residential_post_construction back to the booking_forms service type constraint
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_service_type_check;

ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular', 
    'residential_deep', 
    'residential_move', 
    'residential',
    'residential_post_construction',  -- ✅ ADDED BACK
    
    -- Commercial Services
    'office', 
    'carpet', 
    'window', 
    'construction', 
    'sanitization',
    'tile', 
    'pressure', 
    'floor', 
    'pool', 
    'chimney', 
    'waste-management'
  )
);

-- Add residential_post_construction back to the payment_records service type constraint  
ALTER TABLE payment_records DROP CONSTRAINT IF EXISTS payment_records_service_type_check;

ALTER TABLE payment_records 
ADD CONSTRAINT payment_records_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular',
    'residential_deep', 
    'residential_move',
    'residential',
    'residential_post_construction',  -- ✅ ADDED BACK
    
    -- Commercial Services
    'office',
    'carpet',
    'window', 
    'construction',
    'sanitization',
    'tile',
    'pressure',
    'floor',
    'pool',
    'chimney',
    'waste-management'
  )
);

-- Update the normalization function to handle residential_post_construction
CREATE OR REPLACE FUNCTION normalize_service_type(input_service_type text)
RETURNS text AS $$
BEGIN
  -- Handle null/empty input
  IF input_service_type IS NULL OR input_service_type = '' THEN
    RETURN 'residential';
  END IF;
  
  -- Direct matches for valid service types
  IF input_service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction',  -- ✅ ADDED BACK
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  ) THEN
    RETURN input_service_type;
  END IF;
  
  -- Legacy mappings and normalization
  CASE LOWER(TRIM(input_service_type))
    WHEN 'regular house cleaning', 'regular', 'house' THEN
      RETURN 'residential_regular';
    WHEN 'deep house cleaning', 'deep' THEN
      RETURN 'residential_deep';
    WHEN 'move-in/move-out cleaning', 'move', 'move-out' THEN
      RETURN 'residential_move';
    WHEN 'post-construction cleaning', 'post-construction', 'construction-cleanup' THEN
      RETURN 'residential_post_construction';  -- ✅ ADDED MAPPING
    WHEN 'commercial', 'business', 'office' THEN
      RETURN 'office';
    WHEN 'commercial sanitization service', 'sanitization' THEN
      RETURN 'sanitization';
    ELSE
      -- Default fallback
      RETURN 'residential';
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- Update the validation function too
CREATE OR REPLACE FUNCTION is_valid_service_type(service_type text)
RETURNS boolean AS $$
BEGIN
  RETURN service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction',  -- ✅ ADDED BACK
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  );
END;
$$ LANGUAGE plpgsql;

-- Add helpful comment
COMMENT ON CONSTRAINT booking_forms_service_type_check ON booking_forms 
IS 'Valid service types including residential_post_construction (fixed 2025-01-29)';

COMMENT ON CONSTRAINT payment_records_service_type_check ON payment_records 
IS 'Valid service types including residential_post_construction (fixed 2025-01-29)'; 