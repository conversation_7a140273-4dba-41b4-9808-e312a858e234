// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts";

// CORS headers for browser requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Max-Age": "86400",
};

// Helper function to generate idempotency key
function generateIdempotencyKey(): string {
  return crypto.randomUUID();
}

// Helper function to call Square API directly
async function createSquarePaymentLink(params: {
  accessToken: string;
  environment: string;
  locationId: string;
  amount: number;
  currency: string;
  description: string;
  redirectUrl: string;
  customerEmail?: string;
}) {
  const baseUrl = params.environment === "production" 
    ? "https://connect.squareup.com"
    : "https://connect.squareupsandbox.com";
    
  const idempotencyKey = generateIdempotencyKey();
  
  const requestBody = {
    idempotency_key: idempotencyKey,
    quick_pay: {
      name: params.description,
      price_money: {
        amount: params.amount,
        currency: params.currency
      },
      location_id: params.locationId
    },
    checkout_options: {
      redirect_url: params.redirectUrl,
      ask_for_shipping_address: false,
      merchant_support_email: "<EMAIL>"
    }
  };

  console.log("=== SQUARE API DIRECT CALL DEBUG ===");
  console.log("Base URL:", baseUrl);
  console.log("Request body:", JSON.stringify(requestBody, null, 2));
  
  const response = await fetch(`${baseUrl}/v2/online-checkout/payment-links`, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${params.accessToken}`,
      "Content-Type": "application/json",
      "Square-Version": "2023-10-18"
    },
    body: JSON.stringify(requestBody)
  });

  console.log("Square API response status:", response.status);
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error("Square API error response:", errorText);
    throw new Error(`Square API error: ${response.status} - ${errorText}`);
  }

  const responseData = await response.json();
  console.log("Square API success response:", responseData);
  
  return responseData;
}

// TypeScript interfaces for request data
interface PaymentLinkRequest {
  amount: number;
  currency?: string;
  customerEmail?: string;
  description?: string;
  formData?: Record<string, unknown>;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Simplified service type normalization function
function normalizeServiceType(serviceType: string): string {
  const validTypes = [
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  ];

  if (validTypes.includes(serviceType)) {
    return serviceType;
  }

  return 'residential_regular';
}

// Helper function to safely serialize BigInt values
function serializeResponse(obj: unknown): unknown {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === "bigint") {
    return obj.toString();
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeResponse);
  }

  if (typeof obj === "object") {
    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeResponse(value);
    }
    return result;
  }

  return obj;
}

serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({
        error: "Method not allowed",
        success: false,
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 405,
      }
    );
  }

  try {
    console.log("=== PAYMENT LINK CREATION START ===");
    
    // Get environment variables
    const squareAccessToken = Deno.env.get("SQUARE_ACCESS_TOKEN");
    const squareLocationId = Deno.env.get("SQUARE_LOCATION_ID");
    const squareEnvironment = Deno.env.get("SQUARE_ENVIRONMENT") === "production" ? "production" : "sandbox";

    console.log("Environment check:");
    console.log("- Has SQUARE_ACCESS_TOKEN:", !!squareAccessToken);
    console.log("- Has SQUARE_LOCATION_ID:", !!squareLocationId);
    console.log("- SQUARE_ENVIRONMENT:", squareEnvironment);

    // Validate required environment variables
    if (!squareAccessToken || !squareLocationId) {
      console.error("Missing required Square configuration");
      return new Response(
        JSON.stringify({
          error: "Square API configuration missing",
          success: false,
        }),
        {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
          },
          status: 500,
        }
      );
    }

    // Parse request body
    const requestData: PaymentLinkRequest = await req.json();
    console.log("Request data received:", requestData);

    const { amount, currency, customerEmail, description, formData } = requestData;

    // Validate required fields
    if (!amount || amount <= 0) {
      return new Response(
        JSON.stringify({
          error: "Valid amount is required",
          success: false,
        }),
        {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
          },
          status: 400,
        }
      );
    }

    // Amount is already in cents from frontend
    const amountInCents = Math.round(Number(amount));
    if (amountInCents < 50) {
      throw new Error("Payment amount must be at least $0.50");
    }

    console.log("Creating payment link with:", {
      amount: amountInCents,
      currency: currency || "USD",
      description: description || "Cleaning Service",
      customerEmail,
      environment: squareEnvironment,
    });

    // Generate a unique idempotency key
    const idempotencyKey = generateIdempotencyKey();

    // Create a payment link using direct Square API
    try {
      const response = await createSquarePaymentLink({
        accessToken: squareAccessToken,
        environment: squareEnvironment,
        locationId: squareLocationId,
        amount: amountInCents,
        currency: currency || "USD",
        description: description || "Cleaning Service",
        redirectUrl: `${
          Deno.env.get("PUBLIC_URL") || "http://localhost:5173"
        }/?payment_success=true`,
        customerEmail: customerEmail,
      });

      if (!response?.payment_link) {
        console.error("Square API response missing payment link:", response);
        throw new Error("Failed to create payment link");
      }

      console.log("Payment link created successfully:", {
        id: response.payment_link.id,
        url: response.payment_link.url,
        expiresAt: response.payment_link.expires_at,
      });

      // Store payment link in database
      try {
        const { data: paymentLinkData, error: paymentLinkError } = await supabase
          .from("payment_links")
          .insert({
            payment_link_id: response.payment_link.id,
            square_payment_link_id: response.payment_link.id,
            payment_link_url: response.payment_link.url,
            amount: amount, // Store amount in cents for consistency
            currency: currency || "USD",
            description: description || "Cleaning Service",
            customer_email: customerEmail,
            status: "pending",
            metadata: {
              payment_link_id: response.payment_link.id,
              created_via: "payment_link",
              created_at: new Date().toISOString(),
            },
            expires_at: response.payment_link.expires_at,
            created_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (paymentLinkError) {
          console.error("Error storing payment link:", paymentLinkError);
          // Continue anyway - payment link was created successfully
        }

        const paymentLinkDbId = paymentLinkData?.id;
        console.log("Payment link stored in DB with ID:", paymentLinkDbId);

        // Create payment record
        const metadata = {
          payment_link_db_id: paymentLinkDbId,
          square_payment_link_id: response.payment_link.id,
          created_via: "payment_link_edge_function",
          form_data: formData || {},
          idempotency_key: idempotencyKey,
        };

        const { data: paymentRecord, error: paymentError } = await supabase
          .rpc("create_payment_record", {
            p_amount: amount,
            p_currency: currency || "USD",
            p_service_type: normalizeServiceType(formData?.serviceType as string || "residential_regular"),
            p_description: description,
            p_customer_email: customerEmail,
            p_payment_link_id: response.payment_link.id,
            p_payment_link_url: response.payment_link.url,
            p_order_id: response.payment_link.id,
            p_booking_id: null,
            p_status: "pending",
            p_metadata: metadata,
          });

        if (paymentError) {
          console.error("Error creating payment record:", paymentError);
          // Continue anyway - payment link was created successfully
        }

        const paymentRecordId = paymentRecord;
        console.log("Payment record created with ID:", paymentRecordId);

        return new Response(
          JSON.stringify({
            success: true,
            paymentLink: serializeResponse(response.payment_link),
            paymentRecordId: paymentRecordId,
            paymentUrl: response.payment_link.url,
            paymentLinkId: response.payment_link.id,
          }),
          {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json",
            },
            status: 200,
          }
        );
      } catch (dbError) {
        console.error("Database error:", dbError);
        // Return payment link even if DB storage fails
        return new Response(
          JSON.stringify({
            success: true,
            paymentLink: serializeResponse(response.payment_link),
            paymentUrl: response.payment_link.url,
            paymentLinkId: response.payment_link.id,
            warning: "Payment link created but database storage failed",
          }),
          {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json",
            },
            status: 200,
          }
        );
      }
    } catch (squareError) {
      console.error("Detailed Square API Error:", {
        error: squareError,
        message: squareError.message,
        stack: squareError.stack,
      });
      throw new Error(`Square API Error: ${squareError.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error("Error creating payment link:", error);

    return new Response(
      JSON.stringify({
        error: error.message || "Failed to create payment link",
        success: false,
        details: {
          timestamp: new Date().toISOString(),
        },
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 500,
      }
    );
  }
});
