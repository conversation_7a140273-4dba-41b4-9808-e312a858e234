import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { Client, Environment } from "npm:square";

// CORS headers for browser requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

// TypeScript interfaces for request data
interface PaymentLinkRequest {
  amount: number;
  currency?: string;
  customerEmail?: string;
  description?: string;
  formData?: Record<string, unknown>;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Initialize Square client with required environment variables
const squareAccessToken = Deno.env.get("SQUARE_ACCESS_TOKEN");
const squareLocationId = Deno.env.get("SQUARE_LOCATION_ID");
const squareEnvironment = Deno.env.get("SQUARE_ENVIRONMENT") === "production" 
  ? Environment.Production 
  : Environment.Sandbox;

// Validate required environment variables
if (!squareAccessToken || !squareLocationId) {
  console.error("Missing required Square configuration:", {
    hasAccessToken: !!squareAccessToken,
    hasLocationId: !!squareLocationId
  });
}

const squareClient = new Client({
  accessToken: squareAccessToken || "",
  environment: squareEnvironment,
});

// Helper function to safely serialize BigInt values
function serializeResponse(obj: unknown): unknown {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString();
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeResponse);
  }

  if (typeof obj === 'object') {
    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeResponse(value);
    }
    return result;
  }

  return obj;
}

// Simplified service type normalization function
function normalizeServiceType(serviceType: string): string {
  const validTypes = [
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  ];
  
  if (validTypes.includes(serviceType)) {
    return serviceType;
  }
  
  return 'residential_regular';
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders, status: 204 });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ 
        error: "Method not allowed",
        success: false 
      }),
      {
        headers: { 
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 405,
      }
    );
  }

  try {
    // Log environment variables for debugging
    console.log("Edge Function Environment:", {
      environment: squareEnvironment,
      hasAccessToken: !!squareAccessToken,
      accessTokenLength: squareAccessToken ? squareAccessToken.length : 0,
      hasLocationId: !!squareLocationId,
      locationId: squareLocationId,
      publicUrl: Deno.env.get("PUBLIC_URL") || "http://localhost:5173"
    });

    // Validate Square configuration
    if (!squareAccessToken || !squareLocationId) {
      throw new Error("Square payment system is not properly configured. Please check environment variables.");
    }

    // Parse request body
    const requestData = await req.json().catch((e) => {
      console.error("Failed to parse request body:", e);
      return null;
    }) as PaymentLinkRequest | null;
    
    if (!requestData) {
      throw new Error("Invalid request body");
    }

    console.log("Request data received:", {
      amount: requestData.amount,
      currency: requestData.currency,
      customerEmail: requestData.customerEmail,
      description: requestData.description,
      hasFormData: !!requestData.formData
    });

    const { amount, currency = "USD", customerEmail, description, formData } = requestData;

    // Validate required parameters
    if (!amount || amount <= 0) {
      throw new Error("Invalid payment amount");
    }

    // Amount is already in cents from frontend
    const amountInCents = Math.round(Number(amount));
    if (amountInCents < 50) {
      throw new Error("Payment amount must be at least $0.50");
    }

    // Generate a unique idempotency key
    const idempotencyKey = crypto.randomUUID();

    console.log("Creating payment link with:", {
      amount: amountInCents,
      currency,
      description,
      customerEmail: customerEmail || "Not provided",
      locationId: squareLocationId,
      idempotencyKey: idempotencyKey
    });

    // Create a payment link
    try {
      const response = await squareClient.checkoutApi.createPaymentLink({
        idempotencyKey,
        quickPay: {
          name: description || "Cleaning Service",
          priceMoney: {
            amount: BigInt(amountInCents),
            currency: currency || "USD",
          },
          locationId: squareLocationId,
        },
        checkoutOptions: {
          redirectUrl: `${Deno.env.get("PUBLIC_URL") || "http://localhost:5173"}/?payment_success=true`,
          askForShippingAddress: false,
          merchantSupportEmail: "<EMAIL>",
          ...(customerEmail && { 
            prePopulateBuyerEmail: customerEmail 
          }),
        }
      });

      if (!response?.result?.paymentLink) {
        console.error("Square API response missing payment link:", response);
        throw new Error("Failed to create payment link");
      }

      console.log("Payment link created successfully:", {
        id: response.result.paymentLink.id,
        url: response.result.paymentLink.url,
        expiresAt: response.result.paymentLink.expiresAt
      });

      // Store payment link in Supabase for tracking
      let paymentLinkDbId = null;
      try {
        const { data: paymentLinkData, error: dbError } = await supabase
          .from("payment_links")
          .insert({
            payment_link_id: response.result.paymentLink.id,
            square_payment_link_id: response.result.paymentLink.id,
            payment_link_url: response.result.paymentLink.url,
            amount: amount, // Store amount in cents for consistency
            currency: currency || "USD",
            description: description,
            customer_email: customerEmail,
            status: "created",
            metadata: {
              created_at: new Date().toISOString(),
              environment: squareEnvironment,
              form_data: formData || {},
              amount_cents: amount
            },
          })
          .select()
          .single();

        if (dbError) {
          console.error("Error storing payment link in database:", dbError);
          // Continue even if storage fails
        } else {
          console.log("Payment link stored in database successfully");
          paymentLinkDbId = paymentLinkData?.id;
        }
      } catch (storageError) {
        console.error("Exception storing payment link in database:", storageError);
        // Continue even if storage fails
      }

      // Normalize and validate service type
      const rawServiceType = formData?.serviceType || formData?.service_type || 'residential';
      const serviceType = normalizeServiceType(typeof rawServiceType === 'string' ? rawServiceType : 'residential');

      console.log("Service type validation successful:", { raw: rawServiceType, normalized: serviceType });

      // Create booking record first
      let bookingId = null;
      try {
        const { data: bookingData, error: bookingError } = await supabase
          .from("booking_forms")
          .insert({
            user_id: formData?.user_id || null,
            service_type: serviceType,
            property_details: formData?.propertyDetails || {},
            service_details: formData?.serviceDetails || { totalPrice: amount / 100 },
            schedule: formData?.schedule || {},
            contact: formData?.contact || {},
            status: 'pending',
            metadata: {
              payment_link_id: response.result.paymentLink.id,
              created_via: 'payment_link',
              created_at: new Date().toISOString()
            }
          })
          .select()
          .single();

        if (bookingError) {
          console.error("Error creating booking record:", bookingError);
        } else {
          bookingId = bookingData.id;
          console.log("Booking record created successfully:", bookingId);
        }
      } catch (bookingException) {
        console.error("Exception creating booking record:", bookingException);
      }

      // Create corresponding payment record using secure function
      let paymentRecordId = null;
      try {
        const metadata = {
          payment_link_db_id: paymentLinkDbId,
          square_payment_link_id: response.result.paymentLink.id,
          created_via: 'payment_link_edge_function',
          form_data: formData || {},
          amount_cents: amount,
          normalized_service_type: serviceType,
          original_service_type: formData?.serviceType || formData?.service_type,
          created_at: new Date().toISOString()
        };

        // Use the secure function to bypass RLS issues
        const { data: paymentRecordResult, error: paymentRecordError } = await supabase
          .rpc('create_payment_record_secure', {
            p_user_id: formData?.user_id || null,
            p_service_type: serviceType,
            p_amount: amount, // Amount is already in cents
            p_currency: currency || "USD",
            p_description: description,
            p_customer_email: customerEmail,
            p_payment_link_id: response.result.paymentLink.orderId,
            p_payment_link_url: response.result.paymentLink.url,
            p_order_id: response.result.paymentLink.orderId,
            p_booking_id: bookingId,
            p_status: 'pending',
            p_total_price: amount / 100, // Convert to dollars for total_price field
            p_metadata: metadata
          });

        if (paymentRecordError) {
          console.error("Error creating payment record:", paymentRecordError);
          // Continue even if this fails - webhook will create it if needed
        } else {
          paymentRecordId = paymentRecordResult;
          console.log("Payment record created successfully:", paymentRecordId);
        }
      } catch (paymentRecordException) {
        console.error("Exception creating payment record:", paymentRecordException);
        // Continue even if this fails
      }

      // Return the successful response
      return new Response(
        JSON.stringify({
          success: true,
          paymentLink: serializeResponse(response.result.paymentLink),
          paymentRecordId: paymentRecordId,
          paymentUrl: response.result.paymentLink.url,
          paymentLinkId: response.result.paymentLink.id
        }),
        {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
          },
          status: 200,
        }
      );
    } catch (squareError) {
      console.error("Square API error:", squareError);
      console.error("Square API error details:", JSON.stringify({
        message: squareError.message,
        code: squareError.code,
        statusCode: squareError.statusCode,
        errors: squareError.errors
      }));
      throw new Error(`Square API Error: ${squareError.message}`);
    }
  } catch (error) {
    console.error("Error creating payment link:", error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message || "Failed to create payment link",
        success: false,
        details: {
          hasAccessToken: !!squareAccessToken,
          hasLocationId: !!squareLocationId,
          environment: squareEnvironment,
          timestamp: new Date().toISOString()
        }
      }),
      {
        headers: { 
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 500,
      }
    );
  }
});