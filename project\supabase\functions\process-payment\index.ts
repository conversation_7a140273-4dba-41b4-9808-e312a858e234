// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { Client, Environment } from "npm:square";

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Initialize Square client
const squareAccessToken = Deno.env.get("SQUARE_ACCESS_TOKEN");
const squareLocationId = Deno.env.get("SQUARE_LOCATION_ID");
const squareEnvironment = Deno.env.get("SQUARE_ENVIRONMENT") === "production" 
  ? Environment.Production 
  : Environment.Sandbox;

const squareClient = new Client({
  accessToken: squareAccessToken || "",
  environment: squareEnvironment,
});

// Helper function to normalize service type
function normalizeServiceType(serviceType: string): string {
  if (!serviceType) return 'residential_regular';

  const validTypes = [
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  ];
  
  // Direct match first
  if (validTypes.includes(serviceType)) {
    return serviceType;
  }
  
  // Simple fallback for common cases
  const lowerType = serviceType.toLowerCase().trim();
  if (lowerType.includes('deep')) return 'residential_deep';
  if (lowerType.includes('move')) return 'residential_move';
  if (lowerType.includes('office')) return 'office';
  if (lowerType.includes('carpet')) return 'carpet';
  if (lowerType.includes('window')) return 'window';
  if (lowerType.includes('construction')) return 'construction';
  if (lowerType.includes('pool')) return 'pool';
  
  // Default to residential_regular
  return 'residential_regular';
}

serve(async (req) => {
  // CORS headers for browser requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders, status: 204 });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed", success: false }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 405,
      }
    );
  }

  try {
    // Parse request body
    const requestData = await req.json().catch((e) => {
      console.error("Failed to parse request body:", e);
      return null;
    });
    
    if (!requestData) {
      throw new Error("Invalid request body");
    }

    const { 
      sourceId, 
      amount, 
      bookingId, 
      userId, 
      customerEmail, 
      description, 
      serviceType = 'residential_regular',
      formData = {}
    } = requestData;

    // Validate required parameters
    if (!sourceId) {
      throw new Error("Payment source ID is required");
    }

    if (!amount || amount <= 0) {
      throw new Error("Invalid payment amount");
    }

    // Generate a unique idempotency key
    const idempotencyKey = crypto.randomUUID();

    console.log("Processing payment with:", {
      sourceId,
      amount,
      bookingId,
      idempotencyKey
    });

    // Process the payment with Square
    const paymentResponse = await squareClient.paymentsApi.createPayment({
      sourceId,
      idempotencyKey,
      amountMoney: {
        amount: BigInt(amount),
        currency: 'USD',
      },
      locationId: squareLocationId,
      note: description || 'Cleaning Service',
      ...(customerEmail && { buyerEmailAddress: customerEmail }),
      // Reference the booking ID for tracking
      referenceId: bookingId || 'no-booking-id',
    });

    if (!paymentResponse?.result?.payment) {
      console.error("Square API response missing payment:", paymentResponse);
      throw new Error("Failed to process payment");
    }

    const payment = paymentResponse.result.payment;
    console.log("Payment processed successfully:", {
      id: payment.id,
      status: payment.status,
      amount: payment.amountMoney?.amount?.toString(),
      currency: payment.amountMoney?.currency
    });

    // If we have a booking ID, update the booking status
    if (bookingId) {
      try {
        const { error: bookingError } = await supabase
          .from("booking_forms")
          .update({
            status: 'confirmed',
            payment_status: 'completed',
            updated_at: new Date().toISOString(),
          })
          .eq("id", bookingId);

        if (bookingError) {
          console.error("Error updating booking status:", bookingError);
        } else {
          console.log("Booking status updated to confirmed:", bookingId);
        }
      } catch (bookingException) {
        console.error("Exception updating booking status:", bookingException);
      }
    }

    // Create a payment record
    let paymentRecordId = null;
    try {
      const normalizedServiceType = normalizeServiceType(serviceType);
      
      const { data: paymentRecord, error: paymentRecordError } = await supabase
        .rpc('create_payment_record_secure', {
          p_user_id: userId || null,
          p_service_type: normalizedServiceType,
          p_amount: amount, // Amount is already in cents
          p_currency: 'USD',
          p_description: description || 'Cleaning Service',
          p_customer_email: customerEmail || null,
          p_payment_link_id: null, // No payment link for direct payments
          p_payment_link_url: null, // No payment link for direct payments
          p_order_id: payment.orderId || null,
          p_booking_id: bookingId || null,
          p_status: 'completed', // Payment is already completed
          p_total_price: amount / 100, // Convert cents to dollars for display
          p_metadata: {
            square_payment_id: payment.id,
            payment_status: payment.status,
            created_via: 'web_payments_sdk',
            created_at: new Date().toISOString(),
            form_data: formData
          }
        });

      if (paymentRecordError) {
        console.error("Error creating payment record:", paymentRecordError);
      } else {
        paymentRecordId = paymentRecord;
        console.log("Payment record created successfully:", paymentRecordId);
      }
    } catch (paymentRecordException) {
      console.error("Exception creating payment record:", paymentRecordException);
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        payment: {
          id: payment.id,
          status: payment.status,
          amount: payment.amountMoney?.amount?.toString(),
          currency: payment.amountMoney?.currency,
        },
        paymentRecordId,
        bookingId
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error processing payment:", error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message || "Failed to process payment",
        success: false,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});