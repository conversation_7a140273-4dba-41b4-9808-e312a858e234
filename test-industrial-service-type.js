/**
 * Test script to debug Industrial service type normalization
 * This will help us understand why Industrial forms are being saved as "Office Cleaning"
 */

// Import the service type functions
const { normalizeServiceType, getServiceDbValue, isValidServiceType } = require('./project/src/lib/services/serviceTypeRegistry.ts');

console.log('=== TESTING INDUSTRIAL SERVICE TYPE NORMALIZATION ===');

// Test cases for industrial service type
const testCases = [
  'industrial',
  'industrial-cleaning', 
  'Industrial',
  'Industrial Cleaning',
  'INDUSTRIAL',
  'factory-cleaning',
  'warehouse-cleaning'
];

console.log('\n1. Testing normalizeServiceType function:');
testCases.forEach(input => {
  try {
    const normalized = normalizeServiceType(input);
    console.log(`  "${input}" -> "${normalized}"`);
  } catch (error) {
    console.log(`  "${input}" -> ERROR: ${error.message}`);
  }
});

console.log('\n2. Testing isValidServiceType function:');
testCases.forEach(input => {
  try {
    const normalized = normalizeServiceType(input);
    const isValid = isValidServiceType(normalized);
    console.log(`  "${input}" (normalized: "${normalized}") -> Valid: ${isValid}`);
  } catch (error) {
    console.log(`  "${input}" -> ERROR: ${error.message}`);
  }
});

console.log('\n3. Testing getServiceDbValue function:');
testCases.forEach(input => {
  try {
    const normalized = normalizeServiceType(input);
    const dbValue = getServiceDbValue(normalized);
    console.log(`  "${input}" -> normalized: "${normalized}" -> dbValue: "${dbValue}"`);
  } catch (error) {
    console.log(`  "${input}" -> ERROR: ${error.message}`);
  }
});

console.log('\n4. Testing ServiceTypeStandardizer functions:');
// We'll need to import this differently since it's TypeScript
console.log('  (This will require running in the actual project environment)');

console.log('\n=== END TEST ===');
